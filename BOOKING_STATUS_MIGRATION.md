# Migrasi Halaman Booking Status ke Transaksi

## Overview
Halaman booking-status telah dipindahkan dari `/detail-psychologist/[id]/konseling/booking-status` ke `/transaksi/booking-status` agar navigasi transaksi tetap active setelah proses booking selesai.

## Perubahan yang Dilakukan

### 1. Frontend Changes

#### File Baru
- `src/app/(user)/transaksi/booking-status/page.tsx` - Halaman booking status yang baru

#### File yang Diupdate
- `src/constans/routes.ts` - Menambahkan route baru:
  - `UserTransaksi: '/transaksi'`
  - `UserBookingStatus: '/transaksi/booking-status'`
  - `UserTransaksiDetail: '/transaksi/[status]/[id]'`

#### Fitur yang Ditambahkan
- Integrasi localStorage untuk data booking
- Navigasi otomatis ke detail transaksi setelah payment success
- Cleanup localStorage setelah berhasil redirect
- Support untuk transaction ID dari URL params dan localStorage

### 2. Backend Changes Required

#### Payment Gateway Configuration
Backend perlu mengupdate redirect URL untuk payment gateway:

**Sebelum:**
```
Success URL: {baseUrl}/detail-psychologist/{id}/konseling/booking-status?status=success&transaction_id={id}
Failed URL: {baseUrl}/detail-psychologist/{id}/konseling/booking-status?status=failed&transaction_id={id}
Cancel URL: {baseUrl}/detail-psychologist/{id}/konseling/booking-status?status=cancel&transaction_id={id}
```

**Sesudah:**
```
Success URL: {baseUrl}/transaksi/booking-status?status=success&transaction_id={id}
Failed URL: {baseUrl}/transaksi/booking-status?status=failed&transaction_id={id}
Cancel URL: {baseUrl}/transaksi/booking-status?status=cancel&transaction_id={id}
```

#### API Endpoints yang Terlibat
- `POST /api/counselings` - Endpoint untuk membuat counseling session
- `POST /api/counselings/forecast` - Endpoint untuk forecast pembayaran

### 3. Data Flow

#### Proses Booking
1. User mengisi form booking di `/detail-psychologist/[id]/konseling`
2. Data booking disimpan ke localStorage dengan key `konselingBooking`
3. API call ke forecast endpoint
4. API call ke create counseling endpoint
5. Counseling ID disimpan ke localStorage dengan key `currentCounselingId`
6. User diarahkan ke payment gateway URL

#### Setelah Payment
1. Payment gateway mengarahkan ke `/transaksi/booking-status` dengan query params
2. Halaman booking-status membaca data dari localStorage dan URL params
3. Jika payment success, user dapat klik "Lihat Detail Transaksi"
4. User diarahkan ke `/transaksi/Menunggu-konfirmasi-psikolog/{transactionId}`
5. localStorage dibersihkan setelah redirect berhasil

### 4. LocalStorage Keys
- `konselingBooking` - Data booking (psychologist info, schedule, price, etc.)
- `currentCounselingId` - ID counseling session yang dibuat

### 5. URL Parameters
- `status` - Status payment (success, failed, cancel)
- `transaction_id` - ID transaksi dari payment gateway

## Testing

### Test Cases
1. **Payment Success Flow**
   - Lakukan booking konseling
   - Simulasi payment success
   - Verify redirect ke `/transaksi/booking-status?status=success&transaction_id={id}`
   - Verify data booking ditampilkan dengan benar
   - Verify tombol "Lihat Detail Transaksi" mengarah ke detail transaksi
   - Verify localStorage dibersihkan setelah redirect

2. **Payment Failed Flow**
   - Lakukan booking konseling
   - Simulasi payment failed
   - Verify redirect ke `/transaksi/booking-status?status=failed&transaction_id={id}`
   - Verify tampilan error yang sesuai
   - Verify tombol "Lihat Transaksi" dan "Coba Lagi"

3. **Navigation Active State**
   - Verify nav transaksi tetap active di halaman booking-status
   - Verify nav transaksi tetap active setelah redirect ke detail transaksi

### Manual Testing Steps
1. Buka halaman psychologist detail
2. Pilih jadwal dan isi form booking
3. Klik "Bayar Konseling"
4. Simulasi payment success/failed di payment gateway
5. Verify redirect dan tampilan yang sesuai
6. Test navigasi ke detail transaksi

## Deployment Notes

### Frontend Deployment
- Deploy perubahan frontend terlebih dahulu
- Pastikan halaman `/transaksi/booking-status` dapat diakses

### Backend Deployment
- Update konfigurasi payment gateway redirect URL
- Test payment flow di environment staging terlebih dahulu
- Deploy ke production setelah testing berhasil

### Rollback Plan
Jika terjadi masalah:
1. Revert konfigurasi payment gateway ke URL lama
2. Halaman lama masih tersedia di `/detail-psychologist/[id]/konseling/booking-status`
3. Revert frontend changes jika diperlukan

## Monitoring
- Monitor payment success rate setelah deployment
- Monitor error logs untuk redirect issues
- Monitor user behavior di halaman booking-status yang baru
